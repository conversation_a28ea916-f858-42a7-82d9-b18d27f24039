# Test Task Icon and Button Text Feature

## Summary

Đ<PERSON> thành công thêm 2 cột mới vào bảng `activity_tasks`:
- `task_icon`: VARCHAR(255) - <PERSON>ưu trữ icon cho task (emoji hoặc icon identifier)
- `button_text`: VARCHAR(100) - <PERSON><PERSON>u trữ text hiển thị trên button

## Changes Made

### 1. Database Schema
- ✅ Added migration: `migrations/20250905040926.sql`
- ✅ Updated model: `internal/model/activity_task.go`
- ✅ Updated test config: `internal/test/config.go`
- ✅ Updated documentation: `docs/DATABASE_SCHEMA.md`, `docs/ACTIVITY_CASHBACK_TABLES.md`

### 2. GraphQL Schema
- ✅ Updated user schema: `internal/controller/graphql/schemas/activity_cashback.gql`
- ✅ Updated admin schema: `internal/controller/admin/graphql/schemas/admin_activity_cashback.gql`
- ✅ Added fields to CreateTaskInput and UpdateTaskInput

### 3. Resolvers
- ✅ Updated admin resolver: `internal/controller/admin/graphql/resolvers/admin_activity_cashback.go`
- ✅ Updated user resolver: `internal/controller/graphql/resolvers/activity_cashback.go`
- ✅ Updated conversion functions to include new fields

### 4. Task Seeder
- ✅ Updated task seeder: `internal/service/activity_cashback/task_seeder.go`
- ✅ Added icon and button text to daily tasks:
  - Daily Check-in: 📅 "view"
  - Complete one meme trade: 💰 "trade"
  - Complete one derivatives trade: 💰 "trade"
  - View market page: 👀 "view"
  - Consecutive Check-in: 📅 "Completed"
  - Trade for consecutive days: 💰 "trade"

- ✅ Added icon and button text to community tasks:
  - Follow on X: 🐦 "follow"
  - Retweet a post: 🔄 "share"
  - Like a post: ❤️ "view"
  - Join Telegram: 📱 "view"
  - Invite friends: 👥 "share"
  - Share referral link: 🔗 "forward"

## Button Text Examples
Theo yêu cầu của bạn, các button text bao gồm:
- "view" - Cho các task xem/view
- "trade" - Cho các task trading
- "follow" - Cho task follow social media
- "share" - Cho các task share/retweet
- "forward" - Cho task forward/share link
- "Completed" - Sẽ được xử lý ở FE dựa vào Task Status nếu là COMPLETED

## Testing

### GraphQL Queries

#### 1. Get Task Center (User API)
```graphql
query {
  taskCenter {
    success
    message
    data {
      categories {
        category {
          id
          name
          displayName
        }
        tasks {
          task {
            id
            name
            description
            points
            taskIcon
            buttonText
            taskType
            frequency
          }
          progress {
            status
            progressValue
            completionCount
          }
        }
      }
    }
  }
}
```

#### 2. Get All Tasks (Admin API)
```graphql
query {
  adminGetAllTasks {
    id
    name
    description
    points
    taskIcon
    buttonText
    taskType
    frequency
    category {
      name
      displayName
    }
  }
}
```

#### 3. Create Task with Icon and Button Text (Admin API)
```graphql
mutation {
  createTask(input: {
    categoryId: "1"
    name: "Test Task"
    description: "Test task with icon and button"
    taskType: DAILY
    frequency: DAILY
    points: 10
    taskIcon: "🎯"
    buttonText: "Go to test"
    sortOrder: 1
  }) {
    id
    name
    taskIcon
    buttonText
  }
}
```

#### 4. Update Task with Icon and Button Text (Admin API)
```graphql
mutation {
  updateTask(input: {
    id: "task-uuid-here"
    taskIcon: "🚀"
    buttonText: "Go to rocket"
  }) {
    id
    name
    taskIcon
    buttonText
  }
}
```

## Next Steps

1. **Reseed Tasks**: Chạy `make reseed-tasks` để cập nhật các task hiện có với icon và button text mới
2. **Frontend Integration**: Frontend có thể sử dụng `taskIcon` và `buttonText` từ GraphQL response
3. **Status Handling**: Frontend cần xử lý logic hiển thị "Completed" khi task status là COMPLETED

## Build Status
✅ Build successful - All changes compiled without errors
