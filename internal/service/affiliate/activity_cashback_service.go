package affiliate

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"

	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/transaction"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
)

// ActivityCashbackService handles activity transaction cashback calculations and commission distribution
type ActivityCashbackService struct {
	affiliateRepo        repo.AffiliateRepositoryInterface
	userRepo             transaction.UserRepositoryInterface
	levelRepo            repo.LevelRepo
	activityCashbackRepo repo.ActivityCashbackRepositoryInterface
	memeCommissionRepo   transaction.MemeCommissionLedgerRepositoryInterface
}

// NewActivityCashbackService creates a new activity cashback service
func NewActivityCashbackService() *ActivityCashbackService {
	return &ActivityCashbackService{
		affiliateRepo:        repo.NewAffiliateRepository(),
		userRepo:             transaction.NewUserRepository(),
		levelRepo:            repo.NewLevelRepository(),
		activityCashbackRepo: repo.NewActivityCashbackRepository(),
		memeCommissionRepo:   transaction.NewMemeCommissionLedgerRepository(),
	}
}

// ProcessActivityTransactionCashback processes activity transaction cashback and commission distribution
// This method implements the cashback logic for user activities
func (s *ActivityCashbackService) ProcessActivityTransactionCashback(ctx context.Context, affiliateTx *model.AffiliateTransaction) error {
	if affiliateTx == nil {
		return nil
	}
	global.GVA_LOG.Info("Processing activity transaction cashback",
		zap.String("order_id", affiliateTx.OrderID.String()),
		zap.String("user_id", affiliateTx.UserID.String()),
		zap.String("platform_fee_mint", affiliateTx.PlatformFeeMint),
		zap.String("platform_fee_amount", affiliateTx.PlatformFeeAmount.String()),
		zap.String("quote_amount", affiliateTx.QuoteAmount.String()),
		zap.String("fee_rate", affiliateTx.FeeRate.String()),
		zap.String("status", string(affiliateTx.Status)))

	// Update FirstTransactionAt if this is the user's first transaction
	err := s.updateFirstTransactionAt(ctx, affiliateTx)
	if err != nil {
		global.GVA_LOG.Error("Failed to update FirstTransactionAt",
			zap.String("order_id", affiliateTx.OrderID.String()),
			zap.String("user_id", affiliateTx.UserID.String()),
			zap.Error(err))
		// Continue processing even if FirstTransactionAt update fails
	}

	if affiliateTx.Status != "Completed" {
		global.GVA_LOG.Debug("Transaction not completed, skipping cashback",
			zap.String("order_id", affiliateTx.OrderID.String()),
			zap.String("status", string(affiliateTx.Status)))
		return nil
	}

	// Check if cashback already exists for this transaction
	existingCashback, err := s.activityCashbackRepo.GetActivityCashbackByTransactionID(ctx, affiliateTx.ID)
	if err == nil && existingCashback != nil {
		global.GVA_LOG.Debug("Cashback already exists for transaction",
			zap.String("order_id", affiliateTx.OrderID.String()),
			zap.String("cashback_id", existingCashback.ID.String()))
		return nil
	}

	user, err := s.getUserWithLevel(ctx, affiliateTx.UserID)
	if err != nil {
		return fmt.Errorf("failed to get user with level: %w", err)
	}

	solPrice, err := s.getSolPriceForTransaction(ctx, affiliateTx)
	if err != nil {
		return fmt.Errorf("failed to get SOL price: %w", err)
	}

	if affiliateTx.PlatformFeeMint != utils.WSOL_ADDRESS {
		global.GVA_LOG.Warn("Unsupported platform fee mint, skipping cashback")
		return nil
	}

	if affiliateTx.ChainID != (string)(utils.ChainIDSolana) {
		global.GVA_LOG.Warn("Unsupported chain id, skipping cashback")
		return nil
	}

	// fee amount in SOL
	// platformFeeAmount := affiliateTx.PlatformFeeAmount

	// if platformFeeAmount.LessThanOrEqual(decimal.Zero) {
	// 	platformFeeAmount = affiliateTx.QuoteAmount.Mul(affiliateTx.FeeRate).Truncate(utils.SOL_DECIMALS)

	// 	if platformFeeAmount.LessThanOrEqual(decimal.Zero) {
	// 		global.GVA_LOG.Warn("Calculated platform fee amount is zero or negative, skipping cashback",
	// 			zap.String("order_id", affiliateTx.OrderID.String()),
	// 			zap.String("user_id", affiliateTx.UserID.String()),
	// 			zap.String("quote_amount", affiliateTx.QuoteAmount.String()),
	// 			zap.String("platform_fee", affiliateTx.PlatformFeeAmount.String()),
	// 			zap.String("fee_rate", affiliateTx.FeeRate.String()),
	// 		)
	// 		return nil
	// 	}
	// }

	// // calculate cashback amount in SOL
	// cashbackAmountSOL := platformFeeAmount.Mul(user.AgentLevel.MemeFeeRebate).Truncate(utils.SOL_DECIMALS)
	// if cashbackAmountSOL.LessThanOrEqual(decimal.Zero) {
	// 	global.GVA_LOG.Warn("Calculated cashback amount is zero or negative, skipping cashback",
	// 		zap.String("order_id", affiliateTx.OrderID.String()),
	// 		zap.String("user_id", affiliateTx.UserID.String()),
	// 		zap.String("quote_amount", affiliateTx.QuoteAmount.String()),
	// 		zap.String("platform_fee", platformFeeAmount.String()),
	// 		zap.String("fee_rate", affiliateTx.FeeRate.String()),
	// 		zap.String("user_level", user.AgentLevel.Name),
	// 		zap.String("activity_fee_rate", user.AgentLevel.MemeFeeRate.String()),
	// 	)
	// 	return nil
	// }

	// Activity Fee Rate * QuoteAmount * SolPriceSnapshot.Price = feeRate
	totalAmountUSD := solPrice.Price.Mul(affiliateTx.QuoteAmount)
	cashbackAmountUSD := user.AgentLevel.MemeFeeRate.Mul(totalAmountUSD).Truncate(9)
	// cashbackAmountUSD := cashbackAmountSOL.Mul(solPrice.Price).Truncate(9)

	global.GVA_LOG.Info("Calculated activity cashback",
		zap.String("order_id", affiliateTx.OrderID.String()),
		zap.String("user_id", affiliateTx.UserID.String()),
		zap.String("user_level", user.AgentLevel.Name),
		zap.String("quote_amount", affiliateTx.QuoteAmount.String()),
		zap.String("sol_price", solPrice.Price.String()),
		zap.String("activity_fee_rate", user.AgentLevel.MemeFeeRate.String()),
		zap.String("cashback_amount_usd", cashbackAmountUSD.String()),
		// zap.String("cashback_amount_sol", cashbackAmountSOL.String()),
	)

	cashback := &model.ActivityCashback{
		UserID:                 affiliateTx.UserID,
		UserAddress:            affiliateTx.UserAddress,
		Status:                 "PENDING_CLAIM",
		AffiliateTransactionID: affiliateTx.ID,
		SolPriceUSD:            solPrice.Price,
		CashbackAmountUSD:      cashbackAmountUSD,
		CashbackAmountSOL:      affiliateTx.QuoteAmount,
		CreatedAt:              &time.Time{},
	}

	now := time.Now()
	cashback.CreatedAt = &now

	if err := s.activityCashbackRepo.CreateActivityCashback(ctx, cashback); err != nil {
		return fmt.Errorf("failed to create activity cashback record: %w", err)
	}

	global.GVA_LOG.Info("Activity transaction cashback processed successfully",
		zap.String("order_id", affiliateTx.OrderID.String()),
		zap.String("user_id", affiliateTx.UserID.String()),
		zap.String("cashback_id", cashback.ID.String()),
		zap.String("total_cashback_usd", cashbackAmountUSD.String()),
		// zap.String("total_cashback_sol", cashbackAmountSOL.String()),
	)

	// Process meme commission distribution after cashback is created
	// 传入cashbackAmountUSD字段这个手续费，计算返佣。传入user_id看推荐关系
	err = s.processMemeCommission(ctx, affiliateTx, cashbackAmountUSD)
	if err != nil {
		global.GVA_LOG.Error("Failed to process meme commission",
			zap.String("order_id", affiliateTx.OrderID.String()),
			zap.String("user_id", affiliateTx.UserID.String()),
			zap.Error(err))
		// Don't return error here as cashback was already created successfully
		// Commission failure should not affect cashback processing
	}

	return nil
}

func (s *ActivityCashbackService) getUserWithLevel(ctx context.Context, userID uuid.UUID) (*model.User, error) {
	var user model.User
	err := global.GVA_DB.WithContext(ctx).
		Preload("AgentLevel").
		Where("id = ?", userID).
		First(&user).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	return &user, nil
}

// getSolPriceForTransaction gets the SOL price for the transaction
// We try to get the price at the transaction time, or the latest available price
func (s *ActivityCashbackService) getSolPriceForTransaction(ctx context.Context, affiliateTx *model.AffiliateTransaction) (*model.SolPriceSnapshot, error) {
	// If no price found at transaction time, get the latest available price
	latestPrice, err := s.affiliateRepo.GetLatestSolPrice(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get latest SOL price: %w", err)
	}

	global.GVA_LOG.Warn("Using latest SOL price instead of transaction time price",
		zap.String("order_id", affiliateTx.OrderID.String()),
		zap.String("latest_price", latestPrice.Price.String()),
		zap.Time("latest_price_timestamp", latestPrice.Timestamp),
		zap.Time("tx_timestamp", affiliateTx.CreatedAt))

	return latestPrice, nil
}

// processMemeCommission processes meme commission distribution for upline users
func (s *ActivityCashbackService) processMemeCommission(ctx context.Context, affiliateTx *model.AffiliateTransaction, cashbackAmountUSD decimal.Decimal) error {
	// Get referral information for the user
	referralInfo, err := s.getReferralInfo(ctx, affiliateTx.UserID)
	if err != nil {
		global.GVA_LOG.Debug("No referral found for user, skipping meme commission",
			zap.String("user_id", affiliateTx.UserID.String()),
			zap.String("order_id", affiliateTx.OrderID.String()))
		return nil
	}

	if referralInfo.ReferrerID == nil {
		global.GVA_LOG.Debug("User has no referrer, skipping meme commission",
			zap.String("user_id", affiliateTx.UserID.String()),
			zap.String("order_id", affiliateTx.OrderID.String()))
		return nil
	}

	// Get the upline hierarchy for meme commission distribution
	err = s.getUplineHierarchyForMeme(ctx, affiliateTx.UserID, affiliateTx, cashbackAmountUSD)
	if err != nil {
		return fmt.Errorf("failed to process meme commission hierarchy: %w", err)
	}

	return nil
}

// getUplineHierarchyForMeme gets the upline hierarchy for meme commission distribution (up to 3 levels)
func (s *ActivityCashbackService) getUplineHierarchyForMeme(ctx context.Context, userID uuid.UUID, affiliateTx *model.AffiliateTransaction, cashbackAmountUSD decimal.Decimal) error {
	// Get direct referrer (L1)
	referralInfo, err := s.getReferralInfo(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get referral info: %w", err)
	}

	if referralInfo.ReferrerID == nil {
		global.GVA_LOG.Debug("No L1 referrer found for meme commission",
			zap.String("user_id", userID.String()),
			zap.String("order_id", affiliateTx.OrderID.String()))
		return nil
	}

	// Get L1 upline
	l1User, err := s.getUserWithLevel(ctx, *referralInfo.ReferrerID)
	if err != nil {
		return fmt.Errorf("failed to get L1 user with level: %w", err)
	}

	if l1User == nil {
		global.GVA_LOG.Debug("No L1 user found for referral",
			zap.String("referrer_id", referralInfo.ReferrerID.String()),
			zap.String("order_id", affiliateTx.OrderID.String()))
		return nil
	}

	// Create direct commission for L1
	err = s.createMemeCommission(ctx, affiliateTx, l1User, "Direct", cashbackAmountUSD)
	if err != nil {
		return fmt.Errorf("failed to create direct meme commission: %w", err)
	}

	// Get L2 upline (referrer of L1)
	l2ReferralInfo, err := s.getReferralInfo(ctx, *referralInfo.ReferrerID)
	if err != nil || l2ReferralInfo.ReferrerID == nil {
		global.GVA_LOG.Debug("No L2 referrer found for meme commission",
			zap.String("user_id", userID.String()),
			zap.String("order_id", affiliateTx.OrderID.String()))
		return nil
	}

	l2User, err := s.getUserWithLevel(ctx, *l2ReferralInfo.ReferrerID)
	if err != nil {
		return fmt.Errorf("failed to get L2 user with level: %w", err)
	}

	if l2User == nil {
		global.GVA_LOG.Debug("No L2 user found for referral",
			zap.String("referrer_id", l2ReferralInfo.ReferrerID.String()),
			zap.String("order_id", affiliateTx.OrderID.String()))
		return nil
	}

	// Create indirect commission for L2
	err = s.createMemeCommission(ctx, affiliateTx, l2User, "Indirect", cashbackAmountUSD)
	if err != nil {
		return fmt.Errorf("failed to create indirect meme commission: %w", err)
	}

	// Get L3 upline (referrer of L2)
	l3ReferralInfo, err := s.getReferralInfo(ctx, *l2ReferralInfo.ReferrerID)
	if err != nil || l3ReferralInfo.ReferrerID == nil {
		global.GVA_LOG.Debug("No L3 referrer found for meme commission",
			zap.String("user_id", userID.String()),
			zap.String("order_id", affiliateTx.OrderID.String()))
		return nil
	}

	l3User, err := s.getUserWithLevel(ctx, *l3ReferralInfo.ReferrerID)
	if err != nil {
		return fmt.Errorf("failed to get L3 user with level: %w", err)
	}

	if l3User == nil {
		global.GVA_LOG.Debug("No L3 user found for referral",
			zap.String("referrer_id", l3ReferralInfo.ReferrerID.String()),
			zap.String("order_id", affiliateTx.OrderID.String()))
		return nil
	}

	// Create extended commission for L3
	err = s.createMemeCommission(ctx, affiliateTx, l3User, "Extended", cashbackAmountUSD)
	if err != nil {
		return fmt.Errorf("failed to create extended meme commission: %w", err)
	}

	return nil
}

// createMemeCommission creates a meme commission record for the specified user
func (s *ActivityCashbackService) createMemeCommission(ctx context.Context, affiliateTx *model.AffiliateTransaction, recipientUser *model.User, commissionType string, cashbackAmountUSD decimal.Decimal) error {
	// Get agent level for commission rate
	agentLevel, err := s.levelRepo.GetAgentLevelByID(ctx, recipientUser.AgentLevelID)
	if err != nil {
		return fmt.Errorf("failed to get agent level: %w", err)
	}

	if agentLevel == nil {
		global.GVA_LOG.Warn("No agent level found for user, skipping meme commission",
			zap.String("user_id", recipientUser.ID.String()),
			zap.String("order_id", affiliateTx.OrderID.String()),
			zap.String("commission_type", commissionType))
		return nil
	}

	// Calculate commission rate based on type
	var commissionRate decimal.Decimal
	switch commissionType {
	case "Direct":
		commissionRate = agentLevel.DirectCommissionRate
	case "Indirect":
		commissionRate = agentLevel.IndirectCommissionRate
	case "Extended":
		commissionRate = agentLevel.ExtendedCommissionRate
	default:
		return fmt.Errorf("invalid commission type: %s", commissionType)
	}

	// Check if commission record already exists
	var existingCommission model.MemeCommissionLedger
	err = global.GVA_DB.WithContext(ctx).
		Where("source_transaction_id = ? AND recipient_user_id = ? AND source_transaction_type = ?",
			affiliateTx.OrderID.String(), recipientUser.ID, commissionType).
		First(&existingCommission).Error

	if err == nil {
		// Record already exists, skip creation
		global.GVA_LOG.Info("Meme commission already exists",
			zap.String("order_id", affiliateTx.OrderID.String()),
			zap.String("recipient_user_id", recipientUser.ID.String()),
			zap.String("commission_type", commissionType))
		return nil
	}

	// Calculate commission amount in USD
	// Use cashbackAmountUSD (手续费) * commissionRate
	commissionAmountUSD := cashbackAmountUSD.Mul(commissionRate).Truncate(9)

	if commissionAmountUSD.LessThanOrEqual(decimal.Zero) {
		global.GVA_LOG.Warn("Calculated meme commission amount is zero or negative, skipping",
			zap.String("order_id", affiliateTx.OrderID.String()),
			zap.String("recipient_user_id", recipientUser.ID.String()),
			zap.String("commission_type", commissionType),
			zap.String("cashback_amount_usd", cashbackAmountUSD.String()),
			zap.String("commission_rate", commissionRate.String()))
		return nil
	}

	// Create commission record
	now := time.Now()
	// Convert commission rate to percentage string (e.g., 0.30 -> "30%")
	commissionRatePercent := commissionRate.Mul(decimal.NewFromInt(100)).StringFixed(2) + "%"

	commissionLedger := &model.MemeCommissionLedger{
		RecipientUserID:       recipientUser.ID,
		SourceUserID:          affiliateTx.UserID,
		SourceTransactionID:   affiliateTx.OrderID.String(),
		SourceTransactionType: commissionType,
		CommissionAmount:      commissionAmountUSD,
		CommissionAsset:       commissionRatePercent, // Commission rate as percentage
		Status:                "PENDING_CLAIM",
		CreatedAt:             &now,
		UpdatedAt:             &now,
	}

	if err := s.memeCommissionRepo.CreateMemeCommissionLedger(ctx, commissionLedger); err != nil {
		return fmt.Errorf("failed to create meme commission record: %w", err)
	}

	global.GVA_LOG.Info("Created meme commission",
		zap.String("order_id", affiliateTx.OrderID.String()),
		zap.String("recipient_user_id", recipientUser.ID.String()),
		zap.String("commission_type", commissionType),
		zap.String("commission_amount_usd", commissionAmountUSD.String()),
		zap.String("commission_rate", commissionRate.String()))

	return nil
}

// getReferralInfo gets referral information for a user
func (s *ActivityCashbackService) getReferralInfo(ctx context.Context, userID uuid.UUID) (*model.Referral, error) {
	var referral model.Referral
	err := global.GVA_DB.WithContext(ctx).
		Where("user_id = ?", userID).
		First(&referral).Error
	if err != nil {
		return nil, err
	}
	return &referral, nil
}

// updateFirstTransactionAt updates the FirstTransactionAt field for a user if it's their first transaction
func (s *ActivityCashbackService) updateFirstTransactionAt(ctx context.Context, affiliateTx *model.AffiliateTransaction) error {
	// Check if user already has FirstTransactionAt set
	var user model.User
	err := global.GVA_DB.WithContext(ctx).
		Select("id, first_transaction_at").
		Where("id = ?", affiliateTx.UserID).
		First(&user).Error
	if err != nil {
		// If user not found, log it but don't treat as error
		// This might happen if user hasn't been created in the system yet
		global.GVA_LOG.Debug("User not found when updating FirstTransactionAt",
			zap.String("user_id", affiliateTx.UserID.String()),
			zap.String("order_id", affiliateTx.OrderID.String()),
			zap.Error(err))
		return nil
	}

	// If FirstTransactionAt is already set, no need to update
	if user.FirstTransactionAt != nil {
		global.GVA_LOG.Debug("FirstTransactionAt already set for user",
			zap.String("user_id", affiliateTx.UserID.String()),
			zap.String("order_id", affiliateTx.OrderID.String()),
			zap.Time("first_transaction_at", *user.FirstTransactionAt))
		return nil
	}

	// Update FirstTransactionAt with current transaction time
	now := time.Now()
	err = global.GVA_DB.WithContext(ctx).
		Model(&model.User{}).
		Where("id = ?", affiliateTx.UserID).
		Update("first_transaction_at", &now).Error
	if err != nil {
		return fmt.Errorf("failed to update FirstTransactionAt: %w", err)
	}

	global.GVA_LOG.Info("Updated FirstTransactionAt for user",
		zap.String("user_id", affiliateTx.UserID.String()),
		zap.String("order_id", affiliateTx.OrderID.String()),
		zap.Time("first_transaction_at", now))

	return nil
}
